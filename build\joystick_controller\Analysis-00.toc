(['C:\\git-program\\Embedded\\GameBoard\\joystick_controller_final.py'],
 ['C:\\git-program\\Embedded\\GameBoard'],
 ['serial',
  'serial.tools.list_ports',
  'win32api',
  'win32con',
  'win32gui',
  'keyboard',
  'input_method_manager',
  'ctypes.wintypes'],
 [('C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.11.7 (tags/v3.11.7:fa7a6f2, Dec  4 2023, 19:24:49) [MSC v.1937 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('joystick_controller_final',
   'C:\\git-program\\Embedded\\GameBoard\\joystick_controller_final.py',
   'PYSOURCE')],
 [('zipfile',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\zipfile.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\textwrap.py',
   'PYMODULE'),
  ('copy', 'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\copy.py', 'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\gettext.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\typing.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\tempfile.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\string.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\bisect.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\getopt.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\calendar.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\_strptime.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\selectors.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\quopri.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\token.py',
   'PYMODULE'),
  ('lzma', 'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\bz2.py', 'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\pathlib.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\contextlib.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\struct.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip', 'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\gzip.py', 'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\inspect.py',
   'PYMODULE'),
  ('dis', 'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\dis.py', 'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\opcode.py',
   'PYMODULE'),
  ('ast', 'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\ast.py', 'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\signal.py',
   'PYMODULE'),
  ('keyboard',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\keyboard\\__init__.py',
   'PYMODULE'),
  ('keyboard._canonical_names',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\keyboard\\_canonical_names.py',
   'PYMODULE'),
  ('keyboard._generic',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\keyboard\\_generic.py',
   'PYMODULE'),
  ('keyboard._keyboard_event',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\keyboard\\_keyboard_event.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('keyboard._darwinkeyboard',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\keyboard\\_darwinkeyboard.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('keyboard._nixkeyboard',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\keyboard\\_nixkeyboard.py',
   'PYMODULE'),
  ('keyboard._nixcommon',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\keyboard\\_nixcommon.py',
   'PYMODULE'),
  ('glob', 'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\glob.py', 'PYMODULE'),
  ('keyboard._winkeyboard',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\keyboard\\_winkeyboard.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\platform.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\queue.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\__future__.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('input_method_manager',
   'C:\\git-program\\Embedded\\GameBoard\\input_method_manager.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('serial.tools.list_ports',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\serial\\tools\\list_ports.py',
   'PYMODULE'),
  ('serial.tools',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\serial\\tools\\__init__.py',
   'PYMODULE'),
  ('serial.tools.list_ports_common',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\serial\\tools\\list_ports_common.py',
   'PYMODULE'),
  ('serial.tools.list_ports_posix',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\serial\\tools\\list_ports_posix.py',
   'PYMODULE'),
  ('serial.tools.list_ports_osx',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\serial\\tools\\list_ports_osx.py',
   'PYMODULE'),
  ('serial.tools.list_ports_linux',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\serial\\tools\\list_ports_linux.py',
   'PYMODULE'),
  ('serial.tools.list_ports_windows',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\serial\\tools\\list_ports_windows.py',
   'PYMODULE'),
  ('serial.win32',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('serial',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\serial\\__init__.py',
   'PYMODULE'),
  ('serial.serialjava',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.serialcli',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialutil',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE')],
 [('python311.dll',
   'C:\\Users\\<USER>\\.platformio\\python3\\python311.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\.platformio\\python3\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\.platformio\\python3\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\.platformio\\python3\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\.platformio\\python3\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\.platformio\\python3\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\.platformio\\python3\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\.platformio\\python3\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\.platformio\\python3\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\.platformio\\python3\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\.platformio\\python3\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\.platformio\\python3\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\.platformio\\python3\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\.platformio\\python3\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'C:\\Users\\<USER>\\.platformio\\penv\\Lib\\site-packages\\pywin32_system32\\pywintypes311.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\WINDOWS\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'C:\\git-program\\Embedded\\GameBoard\\build\\joystick_controller\\base_library.zip',
   'DATA')],
 [('enum', 'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\enum.py', 'PYMODULE'),
  ('stat', 'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\stat.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('sre_constants',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('collections.abc',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('re._parser',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\re\\__init__.py',
   'PYMODULE'),
  ('io', 'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\io.py', 'PYMODULE'),
  ('operator',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\operator.py',
   'PYMODULE'),
  ('keyword',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\keyword.py',
   'PYMODULE'),
  ('sre_parse',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\sre_parse.py',
   'PYMODULE'),
  ('functools',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\functools.py',
   'PYMODULE'),
  ('heapq',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\heapq.py',
   'PYMODULE'),
  ('sre_compile',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('copyreg',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\copyreg.py',
   'PYMODULE'),
  ('_weakrefset',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('linecache',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\linecache.py',
   'PYMODULE'),
  ('traceback',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\traceback.py',
   'PYMODULE'),
  ('locale',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\locale.py',
   'PYMODULE'),
  ('reprlib',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\reprlib.py',
   'PYMODULE'),
  ('weakref',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\weakref.py',
   'PYMODULE'),
  ('warnings',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\warnings.py',
   'PYMODULE'),
  ('types',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\types.py',
   'PYMODULE'),
  ('codecs',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\codecs.py',
   'PYMODULE'),
  ('abc', 'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\abc.py', 'PYMODULE'),
  ('ntpath',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\ntpath.py',
   'PYMODULE'),
  ('genericpath',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\genericpath.py',
   'PYMODULE'),
  ('posixpath',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\posixpath.py',
   'PYMODULE'),
  ('_collections_abc',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('os', 'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\os.py', 'PYMODULE'),
  ('collections',
   'C:\\Users\\<USER>\\.platformio\\python3\\Lib\\collections\\__init__.py',
   'PYMODULE')])
