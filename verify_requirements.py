#!/usr/bin/env python3
"""
Requirements Verification Script
验证所有依赖是否正确安装并可用
"""

import sys
import importlib
import subprocess

def check_python_version():
    """检查Python版本"""
    print("🐍 Python版本检查:")
    version = sys.version_info
    print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("   ❌ Python版本过低，需要3.7+")
        return False
    else:
        print("   ✅ Python版本符合要求")
        return True

def check_package(package_name, import_name=None, optional=False):
    """检查单个包是否可用"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'Unknown')
        status = "✅" if not optional else "✅ (可选)"
        print(f"   {status} {package_name}: {version}")
        return True
    except ImportError as e:
        status = "❌" if not optional else "⚠️ (可选)"
        print(f"   {status} {package_name}: 未安装 - {e}")
        return not optional  # 如果是可选包，返回True；必需包返回False

def check_core_dependencies():
    """检查核心依赖"""
    print("\n📦 核心依赖检查:")
    
    results = []
    
    # 串口通信
    results.append(check_package("pyserial", "serial"))
    
    # Windows API (仅Windows需要)
    if sys.platform == "win32":
        results.append(check_package("pywin32", "win32api"))
        results.append(check_package("pywin32", "win32con"))
        results.append(check_package("pywin32", "win32gui"))
    else:
        print("   ⏭️  pywin32: 跳过 (非Windows平台)")
    
    # 键盘控制
    results.append(check_package("keyboard", optional=True))
    
    return all(results)

def check_build_tools():
    """检查构建工具"""
    print("\n🛠️ 构建工具检查:")
    
    # PyInstaller
    result = check_package("pyinstaller", optional=True)
    
    return result

def check_standard_library():
    """检查标准库模块"""
    print("\n📚 标准库模块检查:")
    
    standard_modules = [
        "time", "threading", "sys", "ctypes", 
        "collections", "os", "json"
    ]
    
    results = []
    for module in standard_modules:
        results.append(check_package(module, optional=False))
    
    return all(results)

def check_pip_packages():
    """检查pip安装的包"""
    print("\n📋 已安装包列表:")
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "list"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            relevant_packages = []
            for line in lines:
                if any(pkg in line.lower() for pkg in ['serial', 'win32', 'keyboard', 'pyinstaller']):
                    relevant_packages.append(line.strip())
            
            if relevant_packages:
                for pkg in relevant_packages:
                    print(f"   📦 {pkg}")
            else:
                print("   ⚠️  未找到相关包")
        else:
            print("   ❌ 无法获取包列表")
    except Exception as e:
        print(f"   ❌ 检查失败: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("🔍 JoystickController 依赖验证")
    print("=" * 50)
    
    # 检查Python版本
    python_ok = check_python_version()
    
    # 检查核心依赖
    core_ok = check_core_dependencies()
    
    # 检查构建工具
    build_ok = check_build_tools()
    
    # 检查标准库
    stdlib_ok = check_standard_library()
    
    # 显示已安装包
    check_pip_packages()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 验证结果总结:")
    print("=" * 50)
    
    print(f"🐍 Python版本: {'✅ 通过' if python_ok else '❌ 失败'}")
    print(f"📦 核心依赖: {'✅ 通过' if core_ok else '❌ 失败'}")
    print(f"🛠️ 构建工具: {'✅ 通过' if build_ok else '⚠️ 部分可用'}")
    print(f"📚 标准库: {'✅ 通过' if stdlib_ok else '❌ 失败'}")
    
    overall_ok = python_ok and core_ok and stdlib_ok
    
    if overall_ok:
        print("\n🎉 所有必需依赖都已正确安装！")
        print("✅ 可以运行 JoystickController")
        if build_ok:
            print("✅ 可以构建可执行文件")
        else:
            print("⚠️ 构建工具不完整，但不影响程序运行")
    else:
        print("\n❌ 存在缺失的依赖")
        print("请运行以下命令安装:")
        print("   pip install -r requirements.txt")
    
    print("\n" + "=" * 50)
    return overall_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
