@echo off
echo ========================================
echo   JoystickController Release Creator
echo ========================================
echo.

REM Create release directory
set RELEASE_DIR=JoystickController_Release
if exist "%RELEASE_DIR%" (
    echo Removing existing release directory...
    rmdir /s /q "%RELEASE_DIR%"
)

echo Creating release directory: %RELEASE_DIR%
mkdir "%RELEASE_DIR%"

REM Copy executable
echo Copying executable...
copy "dist\JoystickController.exe" "%RELEASE_DIR%\"

REM Copy batch file
echo Copying batch file...
copy "run_joystick_controller.bat" "%RELEASE_DIR%\"

REM Copy README
echo Copying README...
copy "JoystickController_README.md" "%RELEASE_DIR%\"

REM Create a simple start script in the release directory
echo Creating start script...
echo @echo off > "%RELEASE_DIR%\启动手柄控制器.bat"
echo echo ======================================== >> "%RELEASE_DIR%\启动手柄控制器.bat"
echo echo    JoystickController - 游戏手柄控制器 >> "%RELEASE_DIR%\启动手柄控制器.bat"
echo echo ======================================== >> "%RELEASE_DIR%\启动手柄控制器.bat"
echo echo. >> "%RELEASE_DIR%\启动手柄控制器.bat"
echo echo 正在启动手柄控制器... >> "%RELEASE_DIR%\启动手柄控制器.bat"
echo echo. >> "%RELEASE_DIR%\启动手柄控制器.bat"
echo cd /d "%%~dp0" >> "%RELEASE_DIR%\启动手柄控制器.bat"
echo JoystickController.exe >> "%RELEASE_DIR%\启动手柄控制器.bat"
echo echo. >> "%RELEASE_DIR%\启动手柄控制器.bat"
echo echo 手柄控制器已退出。 >> "%RELEASE_DIR%\启动手柄控制器.bat"
echo pause >> "%RELEASE_DIR%\启动手柄控制器.bat"

echo.
echo ========================================
echo Release created successfully!
echo ========================================
echo.
echo Release directory: %RELEASE_DIR%
echo Files included:
echo   - JoystickController.exe (8.8MB)
echo   - run_joystick_controller.bat
echo   - 启动手柄控制器.bat (Chinese)
echo   - JoystickController_README.md
echo.
echo You can now distribute the "%RELEASE_DIR%" folder.
echo.
pause
